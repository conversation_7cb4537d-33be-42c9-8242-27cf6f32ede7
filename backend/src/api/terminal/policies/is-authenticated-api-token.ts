/**
 * Policy für API-Token-Authentifizierung
 * Überprüft, ob ein gültiger API-Token in der Authorization-Header vorhanden ist
 */

export default (policyContext, config, { strapi }) => {
  const { request } = policyContext;
  
  // Prüfe Authorization Header
  const authorization = request.header.authorization;
  
  if (!authorization) {
    return false;
  }

  // Extrahiere Token aus "Bearer TOKEN" Format
  const token = authorization.replace(/^Bearer\s+/, '');
  
  if (!token) {
    return false;
  }

  // Hier könntest du zusätzliche Token-Validierung implementieren
  // Für jetzt erlauben wir alle Bearer-Token
  return true;
};
