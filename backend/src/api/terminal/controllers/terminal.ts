/**
 * terminal controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::terminal.terminal', ({ strapi }) => ({
  // Override find method to add debugging capability
  async find(ctx) {
    console.log('Terminal GET request received:', {
      query: ctx.query,
      headers: ctx.request.headers,
      user: ctx.state.user,
      timestamp: new Date().toISOString()
    });

    // Call the default find method
    const { data, meta } = await super.find(ctx);

    console.log('Terminal GET response:', {
      count: data?.length || 0,
      meta,
      timestamp: new Date().toISOString()
    });

    return { data, meta };
  },

  // Override findOne method to add debugging capability
  async findOne(ctx) {
    console.log('Terminal GET One request received:', {
      params: ctx.params,
      query: ctx.query,
      headers: ctx.request.headers,
      user: ctx.state.user,
      timestamp: new Date().toISOString()
    });

    // Call the default findOne method
    const { data, meta } = await super.findOne(ctx);

    console.log('Terminal GET One response:', {
      found: !!data,
      documentId: data?.documentId,
      meta,
      timestamp: new Date().toISOString()
    });

    return { data, meta };
  },

  // Override count method to add debugging capability
  async count(ctx) {
    console.log('Terminal COUNT request received:', {
      query: ctx.query,
      headers: ctx.request.headers,
      user: ctx.state.user,
      timestamp: new Date().toISOString()
    });

    // Call the default count method
    const data = await super.count(ctx);

    console.log('Terminal COUNT response:', {
      count: data,
      timestamp: new Date().toISOString()
    });

    return data;
  }
}));
