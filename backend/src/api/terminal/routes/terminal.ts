/**
 * terminal router
 */

import { factories } from '@strapi/strapi';

export default {
  routes: [
    // Custom GET route with API token authentication
    {
      method: 'GET',
      path: '/terminals',
      handler: 'terminal.find',
      config: {
        policies: ['api::terminal.is-authenticated-api-token'],
        middlewares: [],
      }
    },
    {
      method: 'GET',
      path: '/terminals/count',
      handler: 'terminal.count',
      config: {
        policies: ['api::terminal.is-authenticated-api-token'],
        middlewares: [],
      }
    },
    {
      method: 'GET',
      path: '/terminals/:documentId',
      handler: 'terminal.findOne',
      config: {
        policies: ['api::terminal.is-authenticated-api-token'],
        middlewares: [],
      }
    },
    // Include all other standard routes from factory
    ...factories.createCoreRouter('api::terminal.terminal').routes
  ]
};
